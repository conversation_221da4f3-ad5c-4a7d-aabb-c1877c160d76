#!/bin/bash

# Script to convert all images to WebP format with 80% quality
# Supports HEIC and DNG formats

echo "Starting image conversion to WebP format..."
echo "Quality: 80%"
echo ""

# Create a directory for WebP files
mkdir -p webp_output

# Counter for tracking progress
total_files=0
converted_files=0
failed_files=0

# Function to convert a single file
convert_file() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    local name_without_ext="${filename%.*}"
    local output_file="webp_output/${name_without_ext}.webp"
    local temp_file="temp_${name_without_ext}.png"
    
    echo "Processing: $input_file"
    
    # First convert to PNG using sips (handles HEIC and DNG)
    if sips -s format png "$input_file" --out "$temp_file" > /dev/null 2>&1; then
        # Then convert PNG to WebP using cwebp
        if cwebp -q 80 "$temp_file" -o "$output_file" > /dev/null 2>&1; then
            echo "✓ Successfully converted: $output_file"
            ((converted_files++))
        else
            echo "✗ Failed to convert to WebP: $input_file"
            ((failed_files++))
        fi
        # Clean up temporary file
        rm -f "$temp_file"
    else
        echo "✗ Failed to process with sips: $input_file"
        ((failed_files++))
    fi
    
    ((total_files++))
}

# Process all HEIC files
echo "Converting HEIC files..."
for file in *.HEIC; do
    if [ -f "$file" ]; then
        convert_file "$file"
    fi
done

echo ""
echo "Converting DNG files..."
# Process all DNG files
for file in *.DNG; do
    if [ -f "$file" ]; then
        convert_file "$file"
    fi
done

echo ""
echo "Conversion complete!"
echo "Total files processed: $total_files"
echo "Successfully converted: $converted_files"
echo "Failed conversions: $failed_files"
echo ""
echo "WebP files are saved in the 'webp_output' directory"

# Show file sizes comparison
if [ $converted_files -gt 0 ]; then
    echo ""
    echo "File size comparison:"
    echo "Original files total size:"
    du -sh *.HEIC *.DNG 2>/dev/null | awk '{sum+=$1} END {print sum "B total"}'
    echo "WebP files total size:"
    du -sh webp_output/ 2>/dev/null
fi
