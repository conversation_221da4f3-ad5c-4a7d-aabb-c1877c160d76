#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Precise Logo Extractor - Manual analysis and precise cutting of portfolio logos
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter, ImageDraw
import os
from pathlib import Path


def analyze_portfolio_layout(image_path):
    """Analyze the portfolio image to understand the exact layout."""
    print(f"🔍 Analyzing portfolio layout: {image_path}")
    
    image = Image.open(image_path)
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
    
    print(f"📐 Image dimensions: {image.width} x {image.height}")
    
    # Create a visualization to understand the layout
    create_analysis_visualization(image, gray)
    
    return image, gray


def create_analysis_visualization(image, gray):
    """Create a visualization to help understand the image layout."""
    # Analyze horizontal and vertical projections
    h_projection = np.mean(gray, axis=1)  # Average across width
    v_projection = np.mean(gray, axis=0)  # Average across height
    
    # Find significant changes in intensity (likely grid lines)
    h_diff = np.abs(np.diff(h_projection))
    v_diff = np.abs(np.diff(v_projection))
    
    # Find peaks in differences (grid line candidates)
    h_threshold = np.mean(h_diff) + np.std(h_diff) * 1.5
    v_threshold = np.mean(v_diff) + np.std(v_diff) * 1.5
    
    h_peaks = np.where(h_diff > h_threshold)[0]
    v_peaks = np.where(v_diff > v_threshold)[0]
    
    print(f"📊 Detected horizontal transitions: {len(h_peaks)} positions")
    print(f"📊 Detected vertical transitions: {len(v_peaks)} positions")
    
    # Group nearby peaks
    h_lines = group_nearby_points(h_peaks, min_distance=20)
    v_lines = group_nearby_points(v_peaks, min_distance=20)
    
    print(f"🎯 Grouped horizontal lines: {h_lines}")
    print(f"🎯 Grouped vertical lines: {v_lines}")
    
    return h_lines, v_lines


def group_nearby_points(points, min_distance=20):
    """Group nearby points and return the center of each group."""
    if len(points) == 0:
        return []
    
    groups = []
    current_group = [points[0]]
    
    for i in range(1, len(points)):
        if points[i] - points[i-1] <= min_distance:
            current_group.append(points[i])
        else:
            groups.append(int(np.mean(current_group)))
            current_group = [points[i]]
    
    groups.append(int(np.mean(current_group)))
    return groups


def detect_content_regions(gray_image, min_area=5000):
    """Detect actual content regions (logos) in the image."""
    # Apply threshold to separate content from background
    _, thresh = cv2.threshold(gray_image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
    
    # Invert if background is dark
    if np.mean(thresh) < 127:
        thresh = cv2.bitwise_not(thresh)
    
    # Remove noise
    kernel = np.ones((3, 3), np.uint8)
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    thresh = cv2.morphologyEx(thresh, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours by area and aspect ratio
    content_regions = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > min_area:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            
            # Filter reasonable aspect ratios for logos
            if 0.1 < aspect_ratio < 10.0 and w > 50 and h > 50:
                content_regions.append((x, y, w, h, area))
    
    # Sort by position (top to bottom, left to right)
    content_regions.sort(key=lambda r: (r[1], r[0]))
    
    return content_regions


def manual_grid_definition(image_width, image_height):
    """Define a manual grid based on typical portfolio layouts."""
    print("📋 Using manual grid definition for 4x6 portfolio layout")
    
    # Define margins and spacing based on image analysis
    top_margin = int(image_height * 0.05)      # 5% top margin
    bottom_margin = int(image_height * 0.05)   # 5% bottom margin
    left_margin = int(image_width * 0.03)      # 3% left margin
    right_margin = int(image_width * 0.03)     # 3% right margin
    
    # Calculate usable area
    usable_width = image_width - left_margin - right_margin
    usable_height = image_height - top_margin - bottom_margin
    
    # Define 4 rows, 6 columns
    rows, cols = 4, 6
    
    # Calculate cell dimensions
    cell_width = usable_width // cols
    cell_height = usable_height // rows
    
    # Define grid positions
    logo_regions = []
    logo_num = 1
    
    for row in range(rows):
        for col in range(cols):
            # Calculate cell boundaries
            x1 = left_margin + col * cell_width
            y1 = top_margin + row * cell_height
            x2 = x1 + cell_width
            y2 = y1 + cell_height
            
            # Add inner padding to avoid grid lines
            inner_padding = 15
            x1 += inner_padding
            y1 += inner_padding
            x2 -= inner_padding
            y2 -= inner_padding
            
            logo_regions.append((x1, y1, x2 - x1, y2 - y1, logo_num))
            logo_num += 1
    
    return logo_regions


def extract_logo_precisely(image, x, y, w, h):
    """Extract logo with precise content detection and cropping."""
    # Extract the region with some extra padding
    padding = 20
    x_start = max(0, x - padding)
    y_start = max(0, y - padding)
    x_end = min(image.width, x + w + padding)
    y_end = min(image.height, y + h + padding)
    
    logo_region = image.crop((x_start, y_start, x_end, y_end))
    
    # Convert to grayscale for analysis
    gray_logo = logo_region.convert('L')
    gray_array = np.array(gray_logo)
    
    # Find content boundaries using multiple methods
    content_bounds = find_content_boundaries(gray_array)
    
    if content_bounds:
        x1, y1, x2, y2 = content_bounds
        # Add small padding around content
        content_padding = 8
        x1 = max(0, x1 - content_padding)
        y1 = max(0, y1 - content_padding)
        x2 = min(logo_region.width, x2 + content_padding)
        y2 = min(logo_region.height, y2 + content_padding)
        
        logo_crop = logo_region.crop((x1, y1, x2, y2))
    else:
        logo_crop = logo_region
    
    return logo_crop


def find_content_boundaries(gray_array):
    """Find the exact boundaries of content in a grayscale image."""
    # Method 1: Threshold-based detection
    mean_val = np.mean(gray_array)
    std_val = np.std(gray_array)
    
    # Try different threshold strategies
    thresholds = [
        mean_val - std_val * 0.5,
        mean_val - std_val * 1.0,
        mean_val - std_val * 1.5
    ]
    
    best_bounds = None
    best_area = 0
    
    for threshold in thresholds:
        content_mask = gray_array < threshold
        
        # Find bounding box
        rows = np.any(content_mask, axis=1)
        cols = np.any(content_mask, axis=0)
        
        if np.any(rows) and np.any(cols):
            rmin, rmax = np.where(rows)[0][[0, -1]]
            cmin, cmax = np.where(cols)[0][[0, -1]]
            
            area = (rmax - rmin) * (cmax - cmin)
            if area > best_area and area < gray_array.size * 0.8:  # Not too large
                best_area = area
                best_bounds = (cmin, rmin, cmax, rmax)
    
    return best_bounds


def extract_logos_precisely(image_path, output_dir):
    """Extract logos with precise analysis and cutting."""
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Analyze the image
    image, gray = analyze_portfolio_layout(image_path)
    
    # Try content detection first
    print("\n🎯 Attempting automatic content detection...")
    content_regions = detect_content_regions(gray)
    
    if len(content_regions) >= 20:  # If we found reasonable number of regions
        print(f"✅ Found {len(content_regions)} content regions")
        logo_regions = [(x, y, w, h, i+1) for i, (x, y, w, h, area) in enumerate(content_regions)]
    else:
        print(f"⚠️  Only found {len(content_regions)} regions, using manual grid")
        logo_regions = manual_grid_definition(image.width, image.height)
    
    print(f"\n🔧 Extracting {len(logo_regions)} logos with precise cutting...")
    
    saved_count = 0
    for x, y, w, h, logo_num in logo_regions:
        try:
            # Extract with precise content detection
            logo_crop = extract_logo_precisely(image, x, y, w, h)
            
            # Skip very small logos
            if logo_crop.width < 40 or logo_crop.height < 40:
                print(f"  ⏭️  Skipping logo {logo_num:02d}: too small ({logo_crop.width}x{logo_crop.height})")
                continue
            
            # Enhance the logo
            logo_crop = enhance_logo(logo_crop)
            
            # Save the logo
            output_path = os.path.join(output_dir, f"logo_{logo_num:02d}_precise.png")
            logo_crop.save(output_path, 'PNG')
            
            print(f"  ✅ Saved logo {logo_num:02d}: {logo_crop.width}x{logo_crop.height} pixels")
            saved_count += 1
            
        except Exception as e:
            print(f"  ❌ Error extracting logo {logo_num:02d}: {e}")
    
    return saved_count


def enhance_logo(logo_image):
    """Enhance logo quality with multiple techniques."""
    # Enhance contrast
    enhancer = ImageEnhance.Contrast(logo_image)
    logo_image = enhancer.enhance(1.2)
    
    # Enhance sharpness
    enhancer = ImageEnhance.Sharpness(logo_image)
    logo_image = enhancer.enhance(1.1)
    
    # Apply slight denoising
    logo_image = logo_image.filter(ImageFilter.MedianFilter(size=3))
    
    return logo_image


def create_precise_preview(logos_dir, output_path="precise_logo_preview.jpg"):
    """Create a preview of precisely extracted logos."""
    logo_files = [f for f in os.listdir(logos_dir) if f.endswith('_precise.png')]
    logo_files.sort()
    
    if not logo_files:
        print("No precise logo files found!")
        return
    
    print(f"\n🖼️  Creating preview for {len(logo_files)} precise logos...")
    
    # Calculate grid for preview
    cols = 6
    rows = (len(logo_files) + cols - 1) // cols
    
    # Load logos and find max dimensions
    logos = []
    max_width = max_height = 0
    
    for logo_file in logo_files:
        logo = Image.open(os.path.join(logos_dir, logo_file))
        logos.append(logo)
        max_width = max(max_width, logo.width)
        max_height = max(max_height, logo.height)
    
    # Create preview
    margin = 10
    preview_width = cols * max_width + (cols + 1) * margin
    preview_height = rows * max_height + (rows + 1) * margin
    
    preview = Image.new('RGB', (preview_width, preview_height), 'white')
    draw = ImageDraw.Draw(preview)
    
    for i, logo in enumerate(logos):
        row = i // cols
        col = i % cols
        
        # Calculate position (center logo in cell)
        x = col * (max_width + margin) + margin + (max_width - logo.width) // 2
        y = row * (max_height + margin) + margin + (max_height - logo.height) // 2
        
        # Paste logo
        preview.paste(logo, (x, y))
        
        # Add logo number
        logo_num = logo_files[i].replace('logo_', '').replace('_precise.png', '')
        text_x = col * (max_width + margin) + margin + 5
        text_y = row * (max_height + margin) + margin + 5
        
        # Draw text background
        draw.rectangle([text_x-2, text_y-2, text_x+25, text_y+15], fill='black')
        draw.text((text_x, text_y), logo_num, fill='white')
    
    preview.save(output_path, 'JPEG', quality=95)
    print(f"📸 Preview saved: {output_path}")


def main():
    image_path = "extracted_psd_images_compressed/composite_compressed.jpg"
    output_dir = "extracted_logos_precise"
    
    if not os.path.exists(image_path):
        print(f"❌ Error: Image file '{image_path}' not found.")
        return
    
    print("🎯 PRECISE LOGO EXTRACTOR - Content-Aware Cutting")
    print("=" * 60)
    
    count = extract_logos_precisely(image_path, output_dir)
    
    print(f"\n✅ Precise extraction complete! {count} logos saved to '{output_dir}'")
    
    if count > 0:
        # Create preview
        create_precise_preview(output_dir)
        
        # Create compressed versions
        print("\n📦 Creating compressed versions...")
        compress_logos_precise(output_dir)


def compress_logos_precise(input_dir, max_size_kb=300):
    """Compress precisely extracted logos."""
    from io import BytesIO
    
    compressed_dir = os.path.join(input_dir, "compressed")
    Path(compressed_dir).mkdir(exist_ok=True)
    
    logo_files = [f for f in os.listdir(input_dir) if f.endswith('_precise.png')]
    
    for logo_file in sorted(logo_files):
        try:
            input_path = os.path.join(input_dir, logo_file)
            image = Image.open(input_path)
            
            # Convert to RGB for JPEG compression
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image)
                image = background
            
            # Find optimal quality
            quality = 95
            while quality > 10:
                buffer = BytesIO()
                image.save(buffer, format='JPEG', quality=quality, optimize=True)
                size_kb = len(buffer.getvalue()) / 1024
                
                if size_kb <= max_size_kb:
                    break
                quality -= 5
            
            # Save compressed version
            output_path = os.path.join(compressed_dir, logo_file.replace('_precise.png', '_compressed.jpg'))
            image.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            print(f"  ✅ {logo_file}: {size_kb:.1f}KB (quality: {quality})")
            
        except Exception as e:
            print(f"  ❌ Error compressing {logo_file}: {e}")


if __name__ == "__main__":
    main()
