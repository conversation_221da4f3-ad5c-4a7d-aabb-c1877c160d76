#!/usr/bin/env python3
"""
PSD Image Extractor

This script extracts images from Adobe Photoshop PSD files.
It can extract individual layers, layer groups, and the composite image.
"""

import os
import sys
from pathlib import Path
from PIL import Image
import argparse

try:
    from psd_tools import PSDImage
    from psd_tools.api.layers import PixelLayer, Group as GroupLayer
except ImportError:
    print("Error: psd-tools library not found.")
    print("Please install it using: pip install psd-tools")
    sys.exit(1)


def ensure_output_dir(output_dir):
    """Create output directory if it doesn't exist."""
    Path(output_dir).mkdir(parents=True, exist_ok=True)


def sanitize_filename(name):
    """Sanitize layer name to be a valid filename."""
    # Replace invalid characters with underscores
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    
    # Remove leading/trailing spaces and dots
    name = name.strip(' .')
    
    # Ensure it's not empty
    if not name:
        name = "unnamed_layer"
    
    return name


def compress_image(image, max_size_kb=300):
    """Compress image to specified size in KB."""
    if image.mode in ('RGBA', 'LA'):
        # Convert to RGB for JPEG compression
        background = Image.new('RGB', image.size, (255, 255, 255))
        background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
        image = background
    
    # Start with high quality
    quality = 95
    
    while quality > 10:
        # Save to bytes to check size
        from io import BytesIO
        buffer = BytesIO()
        image.save(buffer, format='JPEG', quality=quality, optimize=True)
        size_kb = len(buffer.getvalue()) / 1024
        
        if size_kb <= max_size_kb:
            return image, quality
        
        quality -= 5
    
    return image, quality


def extract_layer_image(layer, output_dir, layer_name, compress=False):
    """Extract image from a single layer."""
    try:
        # Get the layer as PIL Image
        layer_image = layer.topil()
        
        if layer_image is None:
            print(f"  Warning: Could not extract image from layer '{layer_name}'")
            return False
        
        # Sanitize filename
        safe_name = sanitize_filename(layer_name)
        
        if compress:
            # Compress the image
            compressed_image, quality = compress_image(layer_image)
            output_path = os.path.join(output_dir, f"{safe_name}_compressed.jpg")
            compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
            print(f"  Saved compressed layer: {output_path} (quality: {quality})")
        else:
            # Save as PNG to preserve transparency
            output_path = os.path.join(output_dir, f"{safe_name}.png")
            layer_image.save(output_path, 'PNG')
            print(f"  Saved layer: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"  Error extracting layer '{layer_name}': {str(e)}")
        return False


def extract_layers_recursive(layers, output_dir, prefix="", compress=False):
    """Recursively extract all layers and groups."""
    extracted_count = 0
    
    for i, layer in enumerate(layers):
        layer_name = layer.name if layer.name else f"layer_{i}"
        full_name = f"{prefix}{layer_name}" if prefix else layer_name
        
        if isinstance(layer, GroupLayer):
            print(f"Processing group: {full_name}")
            # Create subdirectory for group
            group_dir = os.path.join(output_dir, sanitize_filename(full_name))
            ensure_output_dir(group_dir)
            
            # Recursively process group layers
            group_count = extract_layers_recursive(
                layer, group_dir, "", compress
            )
            extracted_count += group_count
            
        elif isinstance(layer, PixelLayer):
            print(f"Processing layer: {full_name}")
            if extract_layer_image(layer, output_dir, full_name, compress):
                extracted_count += 1
        else:
            print(f"Skipping layer type: {type(layer).__name__} - {full_name}")
    
    return extracted_count


def extract_composite_image(psd, output_dir, compress=False):
    """Extract the composite (flattened) image."""
    try:
        composite = psd.topil()
        if composite is None:
            print("Warning: Could not extract composite image")
            return False
        
        if compress:
            compressed_image, quality = compress_image(composite)
            output_path = os.path.join(output_dir, "composite_compressed.jpg")
            compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
            print(f"Saved compressed composite: {output_path} (quality: {quality})")
        else:
            output_path = os.path.join(output_dir, "composite.png")
            composite.save(output_path, 'PNG')
            print(f"Saved composite: {output_path}")
        
        return True
        
    except Exception as e:
        print(f"Error extracting composite image: {str(e)}")
        return False


def analyze_psd_structure(psd):
    """Analyze and print PSD file structure."""
    print(f"\nPSD File Analysis:")
    print(f"Size: {psd.width} x {psd.height} pixels")
    print(f"Color mode: {psd.color_mode}")
    print(f"Number of layers: {len(psd)}")
    
    def print_layer_tree(layers, indent=0):
        for i, layer in enumerate(layers):
            prefix = "  " * indent
            layer_type = "Group" if isinstance(layer, GroupLayer) else "Layer"
            visible = "✓" if layer.visible else "✗"
            print(f"{prefix}{layer_type}: {layer.name} [{visible}]")
            
            if isinstance(layer, GroupLayer):
                print_layer_tree(layer, indent + 1)
    
    print("\nLayer Structure:")
    print_layer_tree(psd)


def main():
    parser = argparse.ArgumentParser(description='Extract images from PSD files')
    parser.add_argument('psd_file', help='Path to the PSD file')
    parser.add_argument('-o', '--output', default='extracted_images', 
                       help='Output directory (default: extracted_images)')
    parser.add_argument('-c', '--compress', action='store_true',
                       help='Compress images to 300KB max')
    parser.add_argument('--composite-only', action='store_true',
                       help='Extract only the composite image')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze the PSD structure without extracting')
    
    args = parser.parse_args()
    
    # Check if PSD file exists
    if not os.path.exists(args.psd_file):
        print(f"Error: PSD file '{args.psd_file}' not found.")
        sys.exit(1)
    
    try:
        print(f"Loading PSD file: {args.psd_file}")
        psd = PSDImage.open(args.psd_file)
        
        # Analyze structure
        analyze_psd_structure(psd)
        
        if args.analyze_only:
            return
        
        # Create output directory
        ensure_output_dir(args.output)
        print(f"\nExtracting to: {args.output}")
        
        extracted_count = 0
        
        # Extract composite image
        if extract_composite_image(psd, args.output, args.compress):
            extracted_count += 1
        
        # Extract individual layers (unless composite-only)
        if not args.composite_only:
            layer_count = extract_layers_recursive(psd, args.output, "", args.compress)
            extracted_count += layer_count
        
        print(f"\nExtraction complete! {extracted_count} images extracted.")
        
    except Exception as e:
        print(f"Error processing PSD file: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
