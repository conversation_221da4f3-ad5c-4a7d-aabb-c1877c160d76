# PSD Image Extractor

This collection of Python scripts allows you to extract images from Adobe Photoshop PSD files.

## Files Included

1. **`simple_psd_extractor.py`** - Interactive menu-driven extractor
2. **`extract_psd_quick.py`** - Command-line version for quick extraction
3. **`extract_psd_images.py`** - Full-featured extractor with advanced options
4. **`requirements.txt`** - Python dependencies

## Installation

1. Install the required Python libraries:
```bash
pip install psd-tools Pillow
```

Or using the requirements file:
```bash
pip install -r requirements.txt
```

## Usage

### Simple Interactive Extractor

Run the interactive script:
```bash
python3 simple_psd_extractor.py
```

This will show a menu with options:
1. Extract all images (original quality)
2. Extract all images (compressed to 300KB)
3. Extract composite image only
4. Extract composite image only (compressed)

### Quick Command-Line Extractor

Extract all images with compression:
```bash
python3 extract_psd_quick.py --compress
```

Extract only the composite image:
```bash
python3 extract_psd_quick.py --composite-only
```

Extract from a different PSD file:
```bash
python3 extract_psd_quick.py "path/to/your/file.psd" --compress
```

Specify custom output directory:
```bash
python3 extract_psd_quick.py --compress -o "my_output_folder"
```

### Advanced Extractor

For more advanced options:
```bash
python3 extract_psd_images.py "شركاء النجاح copy.psd" --analyze-only
python3 extract_psd_images.py "شركاء النجاح copy.psd" --compress -o extracted_images
```

## What Gets Extracted

The scripts can extract:

- **Composite Image**: The flattened version of the entire PSD
- **Individual Layers**: Each layer as a separate image
- **Layer Groups**: Processed recursively (in advanced version)
- **Text Layers**: Rendered as images
- **Shape Layers**: Rendered as images
- **Pixel Layers**: Direct image content

## Output Formats

- **Original Quality**: PNG format (preserves transparency)
- **Compressed**: JPEG format (compressed to max 300KB file size)

## File Naming

- Layer names are sanitized to be valid filenames
- Invalid characters are replaced with underscores
- Arabic text in layer names is preserved
- Duplicate names get numbered suffixes

## Example Output

For the PSD file "شركاء النجاح copy.psd", you might get:
```
extracted_psd_images_compressed/
├── composite_compressed.jpg
├── Background_compressed.jpg
├── Layer 2_compressed.jpg
├── شركاء النجاح_compressed.jpg
├── Success partners_compressed.jpg
├── Rectangle 1 copy_compressed.jpg
└── Rectangle 1_compressed.jpg
```

## Troubleshooting

### Import Errors
If you get "psd-tools library not found", make sure you're using the correct Python version:
```bash
python3 -m pip install psd-tools Pillow
```

### Large Files
For very large PSD files, the extraction might take some time. The scripts will show progress as they process each layer.

### Unsupported Layers
Some layer types (like adjustment layers) cannot be extracted as images. The script will skip these and report them.

## Features

- ✅ Extracts all layer types that contain image data
- ✅ Preserves layer names (including Arabic text)
- ✅ Automatic image compression to specified file size
- ✅ Progress reporting during extraction
- ✅ Error handling for corrupted or unsupported layers
- ✅ Sanitizes filenames for cross-platform compatibility
- ✅ Supports both PNG (with transparency) and JPEG output

## Requirements

- Python 3.7+
- psd-tools library
- Pillow (PIL) library

## Notes

- The "Unknown image resource 1092" warning is normal and can be ignored
- Group layers themselves don't contain image data, but their child layers do
- Text layers are rendered as images with the current font settings
- Shape layers are rasterized to images
