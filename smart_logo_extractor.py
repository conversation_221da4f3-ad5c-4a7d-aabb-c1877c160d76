#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Smart Logo Extractor - Intelligently detect and extract logos with precise boundaries
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os
from pathlib import Path
import matplotlib.pyplot as plt


def analyze_image_structure(image_path):
    """Analyze the image to understand its structure and find logo boundaries."""
    print(f"Analyzing image structure: {image_path}")
    
    # Load image
    image = Image.open(image_path)
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
    
    print(f"Image dimensions: {image.width} x {image.height}")
    
    # Apply Gaussian blur to reduce noise
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    
    # Use adaptive threshold for better edge detection
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 15, 10)
    
    # Find edges using Canny
    edges = cv2.Canny(blurred, 50, 150, apertureSize=3)
    
    # Detect horizontal and vertical lines (grid structure)
    horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (40, 1))
    vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, 40))
    
    horizontal_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, horizontal_kernel)
    vertical_lines = cv2.morphologyEx(edges, cv2.MORPH_OPEN, vertical_kernel)
    
    # Find line positions
    h_lines = find_line_positions(horizontal_lines, axis=0)  # horizontal lines
    v_lines = find_line_positions(vertical_lines, axis=1)    # vertical lines
    
    print(f"Detected {len(h_lines)} horizontal lines: {h_lines}")
    print(f"Detected {len(v_lines)} vertical lines: {v_lines}")
    
    return image, h_lines, v_lines, gray


def find_line_positions(line_image, axis=0, min_length=100):
    """Find positions of lines in the image."""
    if axis == 0:  # horizontal lines
        projection = np.sum(line_image, axis=1)
    else:  # vertical lines
        projection = np.sum(line_image, axis=0)
    
    # Find peaks in the projection
    lines = []
    threshold = np.max(projection) * 0.3  # 30% of max intensity
    
    for i in range(len(projection)):
        if projection[i] > threshold:
            lines.append(i)
    
    # Group nearby lines and take the center
    grouped_lines = []
    if lines:
        current_group = [lines[0]]
        for i in range(1, len(lines)):
            if lines[i] - lines[i-1] <= 5:  # within 5 pixels
                current_group.append(lines[i])
            else:
                grouped_lines.append(int(np.mean(current_group)))
                current_group = [lines[i]]
        grouped_lines.append(int(np.mean(current_group)))
    
    return grouped_lines


def detect_logo_regions_smart(image, h_lines, v_lines):
    """Detect logo regions using detected grid lines."""
    width, height = image.size
    
    # If we don't have enough lines, fall back to estimated grid
    if len(h_lines) < 3 or len(v_lines) < 5:
        print("Not enough grid lines detected, using estimated grid...")
        return detect_logo_regions_estimated(image)
    
    # Sort lines
    h_lines = sorted(h_lines)
    v_lines = sorted(v_lines)
    
    # Add image boundaries if not present
    if h_lines[0] > 50:
        h_lines.insert(0, 0)
    if h_lines[-1] < height - 50:
        h_lines.append(height)
    
    if v_lines[0] > 50:
        v_lines.insert(0, 0)
    if v_lines[-1] < width - 50:
        v_lines.append(width)
    
    print(f"Using grid lines - H: {h_lines}, V: {v_lines}")
    
    # Create logo regions from grid
    logo_regions = []
    logo_num = 1
    
    for i in range(len(h_lines) - 1):
        for j in range(len(v_lines) - 1):
            x1 = v_lines[j]
            y1 = h_lines[i]
            x2 = v_lines[j + 1]
            y2 = h_lines[i + 1]
            
            # Add padding to avoid grid lines
            padding = 15
            x1 += padding
            y1 += padding
            x2 -= padding
            y2 -= padding
            
            # Ensure valid region
            if x2 > x1 + 50 and y2 > y1 + 50:
                logo_regions.append((x1, y1, x2 - x1, y2 - y1, logo_num))
                logo_num += 1
    
    return logo_regions


def detect_logo_regions_estimated(image):
    """Fallback method using estimated grid positions."""
    width, height = image.size
    
    # Estimate 4x6 grid with better spacing analysis
    rows, cols = 4, 6
    
    # Analyze the image to find better grid boundaries
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
    
    # Find content boundaries by analyzing intensity
    # Horizontal analysis
    h_projection = np.mean(gray, axis=1)
    h_diff = np.diff(h_projection)
    
    # Vertical analysis  
    v_projection = np.mean(gray, axis=0)
    v_diff = np.diff(v_projection)
    
    # Estimate grid positions based on content
    row_height = height // rows
    col_width = width // cols
    
    logo_regions = []
    logo_num = 1
    
    for row in range(rows):
        for col in range(cols):
            # Base grid position
            x1 = col * col_width
            y1 = row * row_height
            x2 = (col + 1) * col_width
            y2 = (row + 1) * row_height
            
            # Refine boundaries by looking for content
            x1, y1, x2, y2 = refine_logo_boundaries(gray, x1, y1, x2, y2)
            
            if x2 > x1 + 30 and y2 > y1 + 30:
                logo_regions.append((x1, y1, x2 - x1, y2 - y1, logo_num))
                logo_num += 1
    
    return logo_regions


def refine_logo_boundaries(gray_image, x1, y1, x2, y2):
    """Refine logo boundaries by finding actual content edges."""
    # Extract the region
    region = gray_image[y1:y2, x1:x2]
    
    if region.size == 0:
        return x1, y1, x2, y2
    
    # Find content boundaries
    # Horizontal boundaries
    h_projection = np.mean(region, axis=1)
    h_threshold = np.mean(h_projection) - np.std(h_projection) * 0.5
    
    content_rows = np.where(h_projection < h_threshold)[0]
    if len(content_rows) > 0:
        top_margin = max(5, content_rows[0] - 10)
        bottom_margin = min(region.shape[0] - 5, content_rows[-1] + 10)
        y1 += top_margin
        y2 = y1 + (bottom_margin - top_margin)
    
    # Vertical boundaries
    v_projection = np.mean(region, axis=0)
    v_threshold = np.mean(v_projection) - np.std(v_projection) * 0.5
    
    content_cols = np.where(v_projection < v_threshold)[0]
    if len(content_cols) > 0:
        left_margin = max(5, content_cols[0] - 10)
        right_margin = min(region.shape[1] - 5, content_cols[-1] + 10)
        x1 += left_margin
        x2 = x1 + (right_margin - left_margin)
    
    return x1, y1, x2, y2


def extract_logo_with_smart_cropping(image, x, y, w, h):
    """Extract logo with smart cropping to remove excess whitespace."""
    # Extract the region
    logo_crop = image.crop((x, y, x + w, y + h))
    
    # Convert to grayscale for analysis
    gray_logo = logo_crop.convert('L')
    gray_array = np.array(gray_logo)
    
    # Find content boundaries
    # Threshold to separate content from background
    threshold = np.mean(gray_array) - np.std(gray_array) * 0.8
    content_mask = gray_array < threshold
    
    # Find bounding box of content
    rows = np.any(content_mask, axis=1)
    cols = np.any(content_mask, axis=0)
    
    if np.any(rows) and np.any(cols):
        rmin, rmax = np.where(rows)[0][[0, -1]]
        cmin, cmax = np.where(cols)[0][[0, -1]]
        
        # Add small padding
        padding = 10
        rmin = max(0, rmin - padding)
        rmax = min(gray_array.shape[0], rmax + padding)
        cmin = max(0, cmin - padding)
        cmax = min(gray_array.shape[1], cmax + padding)
        
        # Crop to content
        logo_crop = logo_crop.crop((cmin, rmin, cmax, rmax))
    
    return logo_crop


def extract_logos_smart(image_path, output_dir):
    """Extract logos with smart detection and cropping."""
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Analyze image structure
    image, h_lines, v_lines, gray = analyze_image_structure(image_path)
    
    # Detect logo regions
    logo_regions = detect_logo_regions_smart(image, h_lines, v_lines)
    
    print(f"\nExtracting {len(logo_regions)} logos with smart cropping...")
    
    saved_count = 0
    for x, y, w, h, logo_num in logo_regions:
        try:
            # Extract with smart cropping
            logo_crop = extract_logo_with_smart_cropping(image, x, y, w, h)
            
            # Skip very small logos
            if logo_crop.width < 30 or logo_crop.height < 30:
                print(f"  Skipping logo {logo_num:02d}: too small after cropping")
                continue
            
            # Enhance the logo
            enhancer = ImageEnhance.Contrast(logo_crop)
            logo_crop = enhancer.enhance(1.3)
            
            enhancer = ImageEnhance.Sharpness(logo_crop)
            logo_crop = enhancer.enhance(1.2)
            
            # Apply slight denoising
            logo_crop = logo_crop.filter(ImageFilter.MedianFilter(size=3))
            
            # Save the logo
            output_path = os.path.join(output_dir, f"logo_{logo_num:02d}_smart.png")
            logo_crop.save(output_path, 'PNG')
            
            print(f"  ✓ Saved logo {logo_num:02d}: {logo_crop.width}x{logo_crop.height} pixels")
            saved_count += 1
            
        except Exception as e:
            print(f"  ✗ Error extracting logo {logo_num:02d}: {e}")
    
    return saved_count


def main():
    image_path = "extracted_psd_images_compressed/composite_compressed.jpg"
    output_dir = "extracted_logos_smart"
    
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found.")
        return
    
    print("🎯 SMART LOGO EXTRACTOR - Precise Boundary Detection")
    print("=" * 60)
    
    count = extract_logos_smart(image_path, output_dir)
    
    print(f"\n✅ Smart extraction complete! {count} logos saved to '{output_dir}'")
    
    # Also create compressed versions
    if count > 0:
        print("\n📦 Creating compressed versions...")
        compress_logos_smart(output_dir)


def compress_logos_smart(input_dir, max_size_kb=300):
    """Compress logos with smart quality adjustment."""
    from io import BytesIO
    
    compressed_dir = os.path.join(input_dir, "compressed")
    Path(compressed_dir).mkdir(exist_ok=True)
    
    logo_files = [f for f in os.listdir(input_dir) if f.endswith('.png')]
    
    for logo_file in sorted(logo_files):
        try:
            input_path = os.path.join(input_dir, logo_file)
            image = Image.open(input_path)
            
            # Convert to RGB for JPEG compression
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image)
                image = background
            
            # Smart quality selection based on content
            quality = 95
            while quality > 15:
                buffer = BytesIO()
                image.save(buffer, format='JPEG', quality=quality, optimize=True)
                size_kb = len(buffer.getvalue()) / 1024
                
                if size_kb <= max_size_kb:
                    break
                quality -= 5
            
            # Save compressed version
            output_path = os.path.join(compressed_dir, logo_file.replace('.png', '_compressed.jpg'))
            image.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            print(f"  ✓ {logo_file}: {size_kb:.1f}KB (quality: {quality})")
            
        except Exception as e:
            print(f"  ✗ Error compressing {logo_file}: {e}")


if __name__ == "__main__":
    main()
