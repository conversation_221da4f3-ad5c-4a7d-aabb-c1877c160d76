#!/bin/bash

# Script to compress WebP images to exactly 300KB
# Uses binary search to find optimal quality setting

echo "Starting WebP compression to 300KB target..."
echo ""

# Target size in bytes (300KB = 300 * 1024 bytes)
TARGET_SIZE=307200
TOLERANCE=5120  # 5KB tolerance

# Create backup and compressed directories
mkdir -p original_backup
mkdir -p compressed_300kb

# Counter for tracking progress
total_files=0
compressed_files=0
already_small_files=0
failed_files=0

# Function to get file size in bytes
get_file_size() {
    stat -f%z "$1" 2>/dev/null || echo 0
}

# Function to compress a single file to target size
compress_to_target() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    local output_file="compressed_300kb/$filename"
    local temp_file="temp_compress_$filename"
    
    echo "Processing: $filename"
    
    # Get current file size
    local current_size=$(get_file_size "$input_file")
    echo "  Current size: $(echo "scale=1; $current_size/1024" | bc)KB"
    
    # If file is already smaller than target + tolerance, just copy it
    if [ $current_size -le $((TARGET_SIZE + TOLERANCE)) ]; then
        cp "$input_file" "$output_file"
        echo "  ✓ File already within target size, copied as-is"
        ((already_small_files++))
        return 0
    fi
    
    # Binary search for optimal quality
    local min_quality=1
    local max_quality=100
    local best_quality=50
    local best_size_diff=999999999
    local iterations=0
    local max_iterations=15
    
    echo "  Finding optimal quality setting..."
    
    while [ $min_quality -le $max_quality ] && [ $iterations -lt $max_iterations ]; do
        local current_quality=$(( (min_quality + max_quality) / 2 ))
        
        # Convert to PNG first, then to WebP with specific quality
        if cwebp -q $current_quality "$input_file" -o "$temp_file" > /dev/null 2>&1; then
            local test_size=$(get_file_size "$temp_file")
            local size_diff=$((test_size - TARGET_SIZE))
            local abs_size_diff=${size_diff#-}  # absolute value
            
            echo "    Quality $current_quality: $(echo "scale=1; $test_size/1024" | bc)KB (diff: $(echo "scale=1; $size_diff/1024" | bc)KB)"
            
            # If this is closer to target, save it as best
            if [ $abs_size_diff -lt $best_size_diff ]; then
                best_quality=$current_quality
                best_size_diff=$abs_size_diff
                cp "$temp_file" "$output_file"
            fi
            
            # If within tolerance, we're done
            if [ $abs_size_diff -le $TOLERANCE ]; then
                echo "    ✓ Found optimal quality: $current_quality"
                break
            fi
            
            # Adjust search range
            if [ $test_size -gt $TARGET_SIZE ]; then
                max_quality=$((current_quality - 1))
            else
                min_quality=$((current_quality + 1))
            fi
        else
            echo "    ✗ Failed to compress with quality $current_quality"
            max_quality=$((current_quality - 1))
        fi
        
        rm -f "$temp_file"
        ((iterations++))
    done
    
    # Verify final result
    if [ -f "$output_file" ]; then
        local final_size=$(get_file_size "$output_file")
        echo "  ✓ Final result: $(echo "scale=1; $final_size/1024" | bc)KB (quality: $best_quality)"
        ((compressed_files++))
        
        # Backup original
        cp "$input_file" "original_backup/$filename"
    else
        echo "  ✗ Failed to compress $filename"
        ((failed_files++))
    fi
    
    # Clean up
    rm -f "$temp_file"
}

# Process all WebP files
echo "Processing WebP files..."
for file in *.webp; do
    if [ -f "$file" ]; then
        compress_to_target "$file"
        echo ""
        ((total_files++))
    fi
done

echo "Compression complete!"
echo "Total files processed: $total_files"
echo "Files compressed: $compressed_files"
echo "Files already small enough: $already_small_files"
echo "Failed compressions: $failed_files"
echo ""
echo "Results:"
echo "- Original files backed up to: original_backup/"
echo "- Compressed files (≤300KB) saved to: compressed_300kb/"

# Show size comparison
if [ $total_files -gt 0 ]; then
    echo ""
    echo "Size comparison:"
    echo "Original total size:"
    du -sh . 2>/dev/null | grep -v compressed_300kb | grep -v original_backup
    echo "Compressed total size:"
    du -sh compressed_300kb/ 2>/dev/null
fi
