#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Logo Separator - Extract individual logos from a portfolio composite image
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
from pathlib import Path
import argparse


def enhance_image_for_detection(image):
    """Enhance image contrast and brightness for better logo detection."""
    # Convert PIL to OpenCV format
    cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
    
    # Convert to grayscale
    gray = cv2.cvtColor(cv_image, cv2.COLOR_BGR2GRAY)
    
    # Apply adaptive threshold to get better contrast
    thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                   cv2.THRESH_BINARY, 11, 2)
    
    # Apply morphological operations to clean up
    kernel = np.ones((3,3), np.uint8)
    cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    cleaned = cv2.morphologyEx(cleaned, cv2.MORPH_OPEN, kernel)
    
    return cleaned, gray


def find_logo_contours(binary_image, min_area=1000, max_area=50000):
    """Find contours that likely represent logos."""
    # Find contours
    contours, _ = cv2.findContours(binary_image, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    # Filter contours by area and aspect ratio
    logo_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h
            
            # Filter by reasonable aspect ratios for logos (not too thin/tall)
            if 0.2 < aspect_ratio < 5.0:
                logo_contours.append((contour, x, y, w, h, area))
    
    # Sort by area (largest first)
    logo_contours.sort(key=lambda x: x[5], reverse=True)
    
    return logo_contours


def detect_logos_grid_based(image, rows=4, cols=6):
    """Detect logos using a grid-based approach for portfolio layouts."""
    width, height = image.size
    
    # Calculate grid cell dimensions
    cell_width = width // cols
    cell_height = height // rows
    
    logo_regions = []
    
    for row in range(rows):
        for col in range(cols):
            # Calculate cell boundaries with some padding
            padding = 20
            x1 = col * cell_width + padding
            y1 = row * cell_height + padding
            x2 = (col + 1) * cell_width - padding
            y2 = (row + 1) * cell_height - padding
            
            # Ensure boundaries are within image
            x1 = max(0, x1)
            y1 = max(0, y1)
            x2 = min(width, x2)
            y2 = min(height, y2)
            
            if x2 > x1 and y2 > y1:
                logo_regions.append((x1, y1, x2 - x1, y2 - y1, row * cols + col + 1))
    
    return logo_regions


def detect_logos_contour_based(image):
    """Detect logos using contour detection."""
    # Enhance image for better detection
    binary_image, gray = enhance_image_for_detection(image)
    
    # Find logo contours
    logo_contours = find_logo_contours(binary_image)
    
    # Convert contours to bounding boxes
    logo_regions = []
    for i, (contour, x, y, w, h, area) in enumerate(logo_contours):
        # Add some padding around the detected region
        padding = 10
        x1 = max(0, x - padding)
        y1 = max(0, y - padding)
        x2 = min(image.width, x + w + padding)
        y2 = min(image.height, y + h + padding)
        
        logo_regions.append((x1, y1, x2 - x1, y2 - y1, i + 1))
    
    return logo_regions


def extract_and_save_logos(image_path, output_dir, method='grid', rows=4, cols=6):
    """Extract logos from the composite image and save them individually."""
    
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    # Load image
    print(f"Loading image: {image_path}")
    image = Image.open(image_path)
    print(f"Image size: {image.width} x {image.height}")
    
    # Detect logo regions
    if method == 'grid':
        print(f"Using grid-based detection ({rows}x{cols})")
        logo_regions = detect_logos_grid_based(image, rows, cols)
    else:
        print("Using contour-based detection")
        logo_regions = detect_logos_contour_based(image)
    
    print(f"Found {len(logo_regions)} logo regions")
    
    # Extract and save each logo
    saved_count = 0
    for i, (x, y, w, h, logo_num) in enumerate(logo_regions):
        try:
            # Extract the logo region
            logo_crop = image.crop((x, y, x + w, y + h))
            
            # Skip very small regions
            if logo_crop.width < 50 or logo_crop.height < 50:
                print(f"  Skipping logo {logo_num}: too small ({logo_crop.width}x{logo_crop.height})")
                continue
            
            # Enhance the logo for better visibility
            enhancer = ImageEnhance.Contrast(logo_crop)
            logo_crop = enhancer.enhance(1.2)
            
            enhancer = ImageEnhance.Sharpness(logo_crop)
            logo_crop = enhancer.enhance(1.1)
            
            # Save the logo
            output_path = os.path.join(output_dir, f"logo_{logo_num:02d}.png")
            logo_crop.save(output_path, 'PNG')
            
            print(f"  ✓ Saved logo {logo_num}: {output_path} ({logo_crop.width}x{logo_crop.height})")
            saved_count += 1
            
        except Exception as e:
            print(f"  ✗ Error extracting logo {logo_num}: {e}")
    
    print(f"\nExtraction complete! {saved_count} logos saved to '{output_dir}'")
    return saved_count


def compress_logos(input_dir, max_size_kb=300):
    """Compress all logos in the directory to specified size."""
    from io import BytesIO
    
    compressed_dir = os.path.join(input_dir, "compressed")
    Path(compressed_dir).mkdir(exist_ok=True)
    
    logo_files = [f for f in os.listdir(input_dir) if f.endswith('.png')]
    
    print(f"\nCompressing {len(logo_files)} logos to max {max_size_kb}KB...")
    
    for logo_file in sorted(logo_files):
        try:
            input_path = os.path.join(input_dir, logo_file)
            image = Image.open(input_path)
            
            # Convert to RGB for JPEG compression
            if image.mode in ('RGBA', 'LA'):
                background = Image.new('RGB', image.size, (255, 255, 255))
                if image.mode == 'RGBA':
                    background.paste(image, mask=image.split()[-1])
                else:
                    background.paste(image)
                image = background
            
            # Find optimal quality
            quality = 95
            while quality > 10:
                buffer = BytesIO()
                image.save(buffer, format='JPEG', quality=quality, optimize=True)
                size_kb = len(buffer.getvalue()) / 1024
                
                if size_kb <= max_size_kb:
                    break
                quality -= 5
            
            # Save compressed version
            output_path = os.path.join(compressed_dir, logo_file.replace('.png', '_compressed.jpg'))
            image.save(output_path, 'JPEG', quality=quality, optimize=True)
            
            print(f"  ✓ Compressed {logo_file}: {size_kb:.1f}KB (quality: {quality})")
            
        except Exception as e:
            print(f"  ✗ Error compressing {logo_file}: {e}")


def main():
    parser = argparse.ArgumentParser(description='Extract individual logos from portfolio composite')
    parser.add_argument('image_path', nargs='?', 
                       default='extracted_psd_images_compressed/composite_compressed.jpg',
                       help='Path to composite image')
    parser.add_argument('-o', '--output', default='extracted_logos',
                       help='Output directory for extracted logos')
    parser.add_argument('-m', '--method', choices=['grid', 'contour'], default='grid',
                       help='Detection method: grid or contour')
    parser.add_argument('-r', '--rows', type=int, default=4,
                       help='Number of rows in grid (for grid method)')
    parser.add_argument('-c', '--cols', type=int, default=6,
                       help='Number of columns in grid (for grid method)')
    parser.add_argument('--compress', action='store_true',
                       help='Also create compressed versions')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.image_path):
        print(f"Error: Image file '{args.image_path}' not found.")
        return
    
    # Extract logos
    count = extract_and_save_logos(args.image_path, args.output, args.method, args.rows, args.cols)
    
    # Compress if requested
    if args.compress and count > 0:
        compress_logos(args.output)


if __name__ == "__main__":
    main()
