#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Logo Preview - Create a preview grid of all extracted logos
"""

from PIL import Image, ImageDraw, ImageFont
import os
import math


def create_logo_preview(logos_dir, output_path="logo_preview.jpg", cols=6):
    """Create a preview grid showing all extracted logos."""
    
    # Get all logo files
    logo_files = [f for f in os.listdir(logos_dir) if f.endswith('.png')]
    logo_files.sort()
    
    if not logo_files:
        print("No logo files found!")
        return
    
    print(f"Creating preview for {len(logo_files)} logos...")
    
    # Calculate grid dimensions
    rows = math.ceil(len(logo_files) / cols)
    
    # Load first logo to get dimensions
    first_logo = Image.open(os.path.join(logos_dir, logo_files[0]))
    logo_width, logo_height = first_logo.size
    
    # Calculate preview dimensions
    margin = 10
    preview_width = cols * logo_width + (cols + 1) * margin
    preview_height = rows * logo_height + (rows + 1) * margin
    
    # Create preview image
    preview = Image.new('RGB', (preview_width, preview_height), 'white')
    
    # Place each logo
    for i, logo_file in enumerate(logo_files):
        row = i // cols
        col = i % cols
        
        # Calculate position
        x = col * (logo_width + margin) + margin
        y = row * (logo_height + margin) + margin
        
        # Load and paste logo
        try:
            logo = Image.open(os.path.join(logos_dir, logo_file))
            preview.paste(logo, (x, y))
            
            # Add logo number
            draw = ImageDraw.Draw(preview)
            logo_num = logo_file.replace('logo_', '').replace('.png', '')
            text_x = x + 5
            text_y = y + 5
            
            # Draw text background
            draw.rectangle([text_x-2, text_y-2, text_x+25, text_y+15], fill='black')
            draw.text((text_x, text_y), logo_num, fill='white')
            
        except Exception as e:
            print(f"Error processing {logo_file}: {e}")
    
    # Save preview
    preview.save(output_path, 'JPEG', quality=90)
    print(f"Preview saved: {output_path}")
    print(f"Preview size: {preview.width}x{preview.height}")


def main():
    logos_dir = "extracted_logos"
    
    if not os.path.exists(logos_dir):
        print(f"Error: Directory '{logos_dir}' not found.")
        return
    
    create_logo_preview(logos_dir)


if __name__ == "__main__":
    main()
