#!/usr/bin/env python3
"""
Simple script to extract images from the specific PSD file
"""

import subprocess
import sys
import os

def main():
    psd_file = "شركاء النجاح copy.psd"
    
    # Check if the PSD file exists
    if not os.path.exists(psd_file):
        print(f"Error: PSD file '{psd_file}' not found in current directory.")
        return
    
    print("PSD Image Extractor")
    print("==================")
    print(f"Target file: {psd_file}")
    print()
    
    # Menu options
    print("Choose extraction option:")
    print("1. Analyze PSD structure only")
    print("2. Extract all images (original quality)")
    print("3. Extract all images (compressed to 300KB)")
    print("4. Extract composite image only")
    print("5. Extract composite image only (compressed)")
    
    choice = input("\nEnter your choice (1-5): ").strip()
    
    # Build command
    cmd = [sys.executable, "extract_psd_images.py", psd_file]
    
    if choice == "1":
        cmd.append("--analyze-only")
    elif choice == "2":
        cmd.extend(["-o", "extracted_psd_images"])
    elif choice == "3":
        cmd.extend(["-o", "extracted_psd_images_compressed", "--compress"])
    elif choice == "4":
        cmd.extend(["-o", "extracted_psd_composite", "--composite-only"])
    elif choice == "5":
        cmd.extend(["-o", "extracted_psd_composite_compressed", "--composite-only", "--compress"])
    else:
        print("Invalid choice. Exiting.")
        return
    
    # Execute the command
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error running extraction: {e}")
    except FileNotFoundError:
        print("Error: extract_psd_images.py not found. Make sure it's in the same directory.")

if __name__ == "__main__":
    main()
