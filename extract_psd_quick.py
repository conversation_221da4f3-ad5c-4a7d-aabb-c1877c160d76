#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Quick PSD Image Extractor - Command Line Version
Usage: python extract_psd_quick.py [--compress] [--composite-only]
"""

import os
import sys
from pathlib import Path
from PIL import Image
import argparse

try:
    from psd_tools import PSDImage
except ImportError:
    print("Error: psd-tools library not found.")
    print("Please install it using: pip install psd-tools")
    sys.exit(1)


def sanitize_filename(name):
    """Sanitize layer name to be a valid filename."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    name = name.strip(' .')
    if not name:
        name = "unnamed_layer"
    return name


def compress_image(image, max_size_kb=300):
    """Compress image to specified size in KB."""
    if image.mode in ('RGBA', 'LA'):
        background = Image.new('RGB', image.size, (255, 255, 255))
        if image.mode == 'RGBA':
            background.paste(image, mask=image.split()[-1])
        else:
            background.paste(image)
        image = background
    
    quality = 95
    while quality > 10:
        from io import BytesIO
        buffer = BytesIO()
        image.save(buffer, format='JPEG', quality=quality, optimize=True)
        size_kb = len(buffer.getvalue()) / 1024
        
        if size_kb <= max_size_kb:
            return image, quality
        quality -= 5
    
    return image, quality


def extract_psd_images(psd_file, output_dir, compress=False, composite_only=False):
    """Extract images from PSD file."""
    
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    print(f"Loading PSD file: {psd_file}")
    try:
        psd = PSDImage.open(psd_file)
    except Exception as e:
        print(f"Error loading PSD file: {e}")
        return 0
    
    print(f"PSD Info: {psd.width}x{psd.height} pixels, {len(psd)} layers")
    print(f"Output directory: {output_dir}")
    print()
    
    extracted_count = 0
    
    # Extract composite image
    print("Extracting composite image...")
    try:
        composite = psd.topil()
        if composite:
            if compress:
                compressed_image, quality = compress_image(composite)
                output_path = os.path.join(output_dir, "composite_compressed.jpg")
                compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
                print(f"  ✓ Saved: composite_compressed.jpg (quality: {quality})")
            else:
                output_path = os.path.join(output_dir, "composite.png")
                composite.save(output_path, 'PNG')
                print(f"  ✓ Saved: composite.png")
            extracted_count += 1
        else:
            print("  ✗ Could not extract composite image")
    except Exception as e:
        print(f"  ✗ Error extracting composite: {e}")
    
    if composite_only:
        print(f"\nExtraction complete! {extracted_count} image extracted.")
        return extracted_count
    
    print("\nExtracting individual layers...")
    for i, layer in enumerate(psd):
        layer_name = layer.name if layer.name else f"layer_{i}"
        print(f"  Processing: {layer_name} ({type(layer).__name__})")
        
        try:
            layer_image = layer.topil()
            
            if layer_image is None:
                print(f"    ✗ Could not extract image from this layer")
                continue
            
            if layer_image.size[0] == 0 or layer_image.size[1] == 0:
                print(f"    ✗ Layer has zero size")
                continue
            
            safe_name = sanitize_filename(layer_name)
            
            if compress:
                compressed_image, quality = compress_image(layer_image)
                output_path = os.path.join(output_dir, f"{safe_name}_compressed.jpg")
                compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
                print(f"    ✓ Saved: {safe_name}_compressed.jpg (quality: {quality})")
            else:
                output_path = os.path.join(output_dir, f"{safe_name}.png")
                layer_image.save(output_path, 'PNG')
                print(f"    ✓ Saved: {safe_name}.png")
            
            extracted_count += 1
            
        except Exception as e:
            print(f"    ✗ Error extracting layer: {e}")
    
    print(f"\nExtraction complete! {extracted_count} images extracted.")
    return extracted_count


def main():
    parser = argparse.ArgumentParser(description='Extract images from PSD file')
    parser.add_argument('psd_file', nargs='?', default="شركاء النجاح copy.psd", 
                       help='Path to PSD file (default: "شركاء النجاح copy.psd")')
    parser.add_argument('--compress', action='store_true',
                       help='Compress images to 300KB max')
    parser.add_argument('--composite-only', action='store_true',
                       help='Extract only the composite image')
    parser.add_argument('-o', '--output', 
                       help='Output directory (auto-generated if not specified)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.psd_file):
        print(f"Error: PSD file '{args.psd_file}' not found.")
        sys.exit(1)
    
    # Auto-generate output directory name
    if not args.output:
        base_name = Path(args.psd_file).stem
        if args.composite_only:
            suffix = "_composite"
        else:
            suffix = "_extracted"
        
        if args.compress:
            suffix += "_compressed"
        
        args.output = f"{base_name}{suffix}"
    
    extract_psd_images(args.psd_file, args.output, args.compress, args.composite_only)


if __name__ == "__main__":
    main()
