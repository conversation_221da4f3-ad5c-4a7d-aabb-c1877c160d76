#!/opt/homebrew/opt/python@3.11/bin/python3.11
"""
Simple PSD Image Extractor
Extracts images from Adobe Photoshop PSD files.
"""

import os
import sys
from pathlib import Path
from PIL import Image

try:
    from psd_tools import PSDImage
except ImportError:
    print("Error: psd-tools library not found.")
    print("Please install it using: pip install psd-tools")
    sys.exit(1)


def sanitize_filename(name):
    """Sanitize layer name to be a valid filename."""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        name = name.replace(char, '_')
    name = name.strip(' .')
    if not name:
        name = "unnamed_layer"
    return name


def compress_image(image, max_size_kb=300):
    """Compress image to specified size in KB."""
    if image.mode in ('RGBA', 'LA'):
        # Convert to RGB for JPEG compression
        background = Image.new('RGB', image.size, (255, 255, 255))
        if image.mode == 'RGBA':
            background.paste(image, mask=image.split()[-1])
        else:
            background.paste(image)
        image = background
    
    # Start with high quality
    quality = 95
    
    while quality > 10:
        # Save to bytes to check size
        from io import BytesIO
        buffer = BytesIO()
        image.save(buffer, format='JPEG', quality=quality, optimize=True)
        size_kb = len(buffer.getvalue()) / 1024
        
        if size_kb <= max_size_kb:
            return image, quality
        
        quality -= 5
    
    return image, quality


def extract_psd_images(psd_file, output_dir="extracted_images", compress=False):
    """Extract all images from PSD file."""
    
    # Create output directory
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    print(f"Loading PSD file: {psd_file}")
    try:
        psd = PSDImage.open(psd_file)
    except Exception as e:
        print(f"Error loading PSD file: {e}")
        return
    
    print(f"PSD Info: {psd.width}x{psd.height} pixels, {len(psd)} layers")
    print(f"Output directory: {output_dir}")
    print()
    
    extracted_count = 0
    
    # Extract composite image
    print("Extracting composite image...")
    try:
        composite = psd.topil()
        if composite:
            if compress:
                compressed_image, quality = compress_image(composite)
                output_path = os.path.join(output_dir, "composite_compressed.jpg")
                compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
                print(f"  ✓ Saved: composite_compressed.jpg (quality: {quality})")
            else:
                output_path = os.path.join(output_dir, "composite.png")
                composite.save(output_path, 'PNG')
                print(f"  ✓ Saved: composite.png")
            extracted_count += 1
        else:
            print("  ✗ Could not extract composite image")
    except Exception as e:
        print(f"  ✗ Error extracting composite: {e}")
    
    print()
    
    # Extract individual layers
    print("Extracting individual layers...")
    for i, layer in enumerate(psd):
        layer_name = layer.name if layer.name else f"layer_{i}"
        print(f"  Processing: {layer_name} ({type(layer).__name__})")
        
        try:
            # Try to get the layer as PIL Image
            layer_image = layer.topil()
            
            if layer_image is None:
                print(f"    ✗ Could not extract image from this layer")
                continue
            
            # Check if image has content (not just transparent)
            if layer_image.size[0] == 0 or layer_image.size[1] == 0:
                print(f"    ✗ Layer has zero size")
                continue
            
            # Sanitize filename
            safe_name = sanitize_filename(layer_name)
            
            if compress:
                # Compress the image
                compressed_image, quality = compress_image(layer_image)
                output_path = os.path.join(output_dir, f"{safe_name}_compressed.jpg")
                compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
                print(f"    ✓ Saved: {safe_name}_compressed.jpg (quality: {quality})")
            else:
                # Save as PNG to preserve transparency
                output_path = os.path.join(output_dir, f"{safe_name}.png")
                layer_image.save(output_path, 'PNG')
                print(f"    ✓ Saved: {safe_name}.png")
            
            extracted_count += 1
            
        except Exception as e:
            print(f"    ✗ Error extracting layer: {e}")
    
    print()
    print(f"Extraction complete! {extracted_count} images extracted to '{output_dir}'")


def main():
    psd_file = "شركاء النجاح copy.psd"
    
    if not os.path.exists(psd_file):
        print(f"Error: PSD file '{psd_file}' not found.")
        return
    
    print("PSD Image Extractor")
    print("==================")
    print()
    
    # Menu
    print("Choose extraction option:")
    print("1. Extract all images (original quality)")
    print("2. Extract all images (compressed to 300KB)")
    print("3. Extract composite image only")
    print("4. Extract composite image only (compressed)")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    if choice == "1":
        extract_psd_images(psd_file, "extracted_psd_images", compress=False)
    elif choice == "2":
        extract_psd_images(psd_file, "extracted_psd_images_compressed", compress=True)
    elif choice == "3":
        # Extract only composite
        output_dir = "extracted_psd_composite"
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        psd = PSDImage.open(psd_file)
        composite = psd.topil()
        if composite:
            output_path = os.path.join(output_dir, "composite.png")
            composite.save(output_path, 'PNG')
            print(f"Composite image saved to: {output_path}")
        else:
            print("Could not extract composite image")
    elif choice == "4":
        # Extract only composite (compressed)
        output_dir = "extracted_psd_composite_compressed"
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        psd = PSDImage.open(psd_file)
        composite = psd.topil()
        if composite:
            compressed_image, quality = compress_image(composite)
            output_path = os.path.join(output_dir, "composite_compressed.jpg")
            compressed_image.save(output_path, 'JPEG', quality=quality, optimize=True)
            print(f"Compressed composite image saved to: {output_path} (quality: {quality})")
        else:
            print("Could not extract composite image")
    else:
        print("Invalid choice.")


if __name__ == "__main__":
    main()
